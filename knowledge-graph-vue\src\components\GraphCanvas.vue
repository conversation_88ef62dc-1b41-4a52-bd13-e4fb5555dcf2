<template>
  <div class="graph-canvas" ref="canvasContainer">
    <!-- Pixi.js 画布将在这里创建 -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as PIXI from 'pixi.js'
import * as d3 from 'd3'
import { useGraphDataStore } from '../stores/graphData.js'
import { useUIStateStore } from '../stores/uiState.js'

// 组件引用
const canvasContainer = ref(null)

// Stores
const graphData = useGraphDataStore()
const uiState = useUIStateStore()

// Pixi.js 应用和容器
let app = null
let backgroundContainer = null
let edgesContainer = null
let nodesContainer = null

// D3 力导向模拟
let simulation = null

// 节点和边的图形对象映射
const nodeGraphics = new Map()
const nodeLabels = new Map()
const edgeGraphics = new Map()

// 视图变换
let viewport = {
  x: 0,
  y: 0,
  scale: 1,
}

// 拖拽状态
let isDragging = false
let dragStart = { x: 0, y: 0 }
let dragTarget = null

// 工具提示状态
let tooltipTimeout = null

// 性能优化相关
let lastFrameTime = 0
let frameCount = 0
let isPerformanceMode = false

// 对象池
const nodePool = []
const edgePool = []

// 视口裁剪
let viewportBounds = { x: 0, y: 0, width: 0, height: 0 }

/**
 * 初始化 Pixi.js 应用
 */
function initPixiApp() {
  if (!canvasContainer.value) return

  // 创建 Pixi 应用
  app = new PIXI.Application({
    width: window.innerWidth - uiState.sidebarWidth,
    height: window.innerHeight,
    backgroundColor: 0x0a0a0a,
    antialias: true,
    resolution: window.devicePixelRatio || 1,
    autoDensity: true,
  })

  // 将画布添加到容器
  canvasContainer.value.appendChild(app.view)

  // 创建容器
  backgroundContainer = new PIXI.Container()
  edgesContainer = new PIXI.Container()
  nodesContainer = new PIXI.Container()

  // 设置容器的事件模式
  edgesContainer.eventMode = 'static'
  nodesContainer.eventMode = 'static'

  app.stage.addChild(backgroundContainer)
  app.stage.addChild(edgesContainer) // 边容器在底层
  app.stage.addChild(nodesContainer) // 节点容器在上层

  // 创建星空背景
  createStarField()

  // 设置交互
  app.stage.eventMode = 'static'
  app.stage.hitArea = new PIXI.Rectangle(0, 0, app.screen.width, app.screen.height)

  // 添加全局鼠标事件监听器用于边检测
  app.stage.on('pointermove', handleGlobalPointerMove)
  app.stage.on('pointerdown', handleGlobalPointerDown)

  // 添加事件监听器
  setupEventListeners()

  // 暴露到全局作用域用于调试
  window.pixiApp = app
  window.pixiNodesContainer = nodesContainer
  window.pixiEdgesContainer = edgesContainer

  console.log('Pixi.js 应用初始化完成')
}

/**
 * 初始化 D3 力导向模拟
 */
function initD3Simulation() {
  simulation = d3
    .forceSimulation()
    .force(
      'link',
      d3
        .forceLink()
        .id((d) => d.id)
        .distance(50)
        .strength(0.1),
    )
    .force('charge', d3.forceManyBody().strength(-300).distanceMax(200))
    .force('center', d3.forceCenter(app.screen.width / 2, app.screen.height / 2))
    .force(
      'collision',
      d3
        .forceCollide()
        .radius((d) => Math.sqrt(d.degree) * 2 + 5)
        .strength(0.7),
    )
    .alphaDecay(0.02)
    .velocityDecay(0.3)

  // 监听 tick 事件
  simulation.on('tick', onSimulationTick)

  console.log('D3 力导向模拟初始化完成')
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
  // 鼠标滚轮缩放
  app.view.addEventListener('wheel', handleWheel, { passive: false })

  // 鼠标拖拽
  app.stage.on('pointerdown', handlePointerDown)
  app.stage.on('pointermove', handlePointerMove)
  app.stage.on('pointerup', handlePointerUp)
  app.stage.on('pointerupoutside', handlePointerUp)

  // 点击空白区域
  app.stage.on('pointerdown', handleStageClick)
}

/**
 * 处理滚轮缩放
 */
function handleWheel(event) {
  event.preventDefault()

  const delta = event.deltaY > 0 ? 0.9 : 1.1
  const newScale = Math.max(0.1, Math.min(5.0, viewport.scale * delta))

  if (newScale !== viewport.scale) {
    viewport.scale = newScale
    updateViewTransform()
    uiState.updateViewTransform({ scale: viewport.scale })
  }
}

/**
 * 处理指针按下
 */
function handlePointerDown(event) {
  isDragging = true
  dragStart = { x: event.data.global.x, y: event.data.global.y }
  dragTarget = event.target

  // 如果点击的是节点，准备拖拽节点
  if (dragTarget && dragTarget.nodeData) {
    const node = simulation.nodes().find((n) => n.id === dragTarget.nodeData.id)
    if (node) {
      // 不固定节点位置，让物理引擎继续工作
      // 但标记节点为被拖拽状态
      node.isDragging = true

      // 记录拖拽开始时的屏幕坐标，用于计算鼠标目标位置
      dragTarget.startScreenX = event.data.global.x
      dragTarget.startScreenY = event.data.global.y

      // 确保物理模拟保持活跃
      if (uiState.physicsRunning) {
        simulation.alpha(0.3).restart()
      }
    }
  }
}

/**
 * 处理指针移动
 */
function handlePointerMove(event) {
  if (!isDragging) return

  const currentPos = { x: event.data.global.x, y: event.data.global.y }

  if (dragTarget && dragTarget.nodeData) {
    // 拖拽节点 - 通过施加引导力来影响节点移动
    const node = simulation.nodes().find((n) => n.id === dragTarget.nodeData.id)
    if (node) {
      // 将当前鼠标位置转换为世界坐标
      const mouseWorldX = (currentPos.x - viewport.x) / viewport.scale
      const mouseWorldY = (currentPos.y - viewport.y) / viewport.scale

      // 计算节点到鼠标位置的向量
      const deltaX = mouseWorldX - node.x
      const deltaY = mouseWorldY - node.y
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

      if (distance > 0) {
        // 计算引导力的强度，距离越远力越大
        let forceStrength = Math.min(distance * 0.3, 150) // 增加力度系数和最大力度

        // 如果距离很大，给予额外的推动力
        if (distance > 100) {
          forceStrength *= 1.5 // 远距离时增加50%的力度
        }

        // 计算单位向量
        const unitX = deltaX / distance
        const unitY = deltaY / distance

        // 施加引导力，增加应用强度
        node.vx = (node.vx || 0) + unitX * forceStrength * 0.4
        node.vy = (node.vy || 0) + unitY * forceStrength * 0.4

        // 增加最大速度限制，让节点移动更快
        const maxVelocity = 30
        const currentVelocity = Math.sqrt(node.vx * node.vx + node.vy * node.vy)
        if (currentVelocity > maxVelocity) {
          node.vx = (node.vx / currentVelocity) * maxVelocity
          node.vy = (node.vy / currentVelocity) * maxVelocity
        }
      }

      // 保持物理模拟活跃
      if (uiState.physicsRunning && simulation.alpha() < 0.1) {
        simulation.alpha(0.1).restart()
      }
    }
  } else {
    // 拖拽整个视图
    const deltaX = currentPos.x - dragStart.x
    const deltaY = currentPos.y - dragStart.y

    // 计算新的视口位置
    const newX = viewport.x + deltaX
    const newY = viewport.y + deltaY

    // 添加边界限制，防止拖动到极端位置导致卡顿
    const maxOffset = 10000 // 最大偏移量，防止数值溢出
    const minOffset = -10000

    viewport.x = Math.max(minOffset, Math.min(maxOffset, newX))
    viewport.y = Math.max(minOffset, Math.min(maxOffset, newY))

    updateViewTransform()
    uiState.updateViewTransform({ x: viewport.x, y: viewport.y })

    dragStart = currentPos
  }
}

/**
 * 处理指针释放
 */
function handlePointerUp(event) {
  if (dragTarget && dragTarget.nodeData) {
    const node = simulation.nodes().find((n) => n.id === dragTarget.nodeData.id)
    if (node) {
      // 清除拖拽状态
      node.isDragging = false

      // 减缓节点速度，让其自然停下（保持更多动量）
      if (node.vx) node.vx *= 0.8
      if (node.vy) node.vy *= 0.8

      // 让物理模拟自然衰减
      if (uiState.physicsRunning) {
        simulation.alpha(0.3).restart()
      }
    }

    // 清除临时存储的拖拽坐标数据
    if (dragTarget.startScreenX !== undefined) {
      delete dragTarget.startScreenX
      delete dragTarget.startScreenY
    }
  }

  isDragging = false
  dragTarget = null
}

/**
 * 处理舞台点击
 */
function handleStageClick(event) {
  // 点击空白区域清除选择
  uiState.clearSelection()
}

/**
 * 更新视图变换
 */
function updateViewTransform() {
  if (!app) return

  app.stage.scale.set(viewport.scale)
  app.stage.position.set(viewport.x, viewport.y)
}

/**
 * 模拟 tick 事件处理
 */
function onSimulationTick() {
  // 调试模拟tick
  if (!window.tickCount) window.tickCount = 0
  window.tickCount++
  if (window.tickCount <= 3) {
    // 详细检查模拟状态
    const nodes = simulation.nodes()
    const links = simulation.force('link').links()
    console.log(`模拟tick ${window.tickCount}:`, {
      alpha: simulation.alpha(),
      nodeCount: nodes.length,
      edgeCount: links.length,
      firstNode: nodes[0] ? { id: nodes[0].id, x: nodes[0].x, y: nodes[0].y } : 'no nodes',
      firstLink: links[0]
        ? { id: links[0].id, source: links[0].source?.id, target: links[0].target?.id }
        : 'no links',
    })
  }

  // 更新视口边界
  updateViewportBounds()

  // 更新节点位置
  nodeGraphics.forEach((graphic, nodeId) => {
    const node = simulation.nodes().find((n) => n.id === nodeId)
    if (node && graphic) {
      graphic.x = node.x
      graphic.y = node.y

      // 更新标签位置
      const label = nodeLabels.get(nodeId)
      if (label) {
        label.x = node.x
        label.y = node.y + getNodeRadius(node.degree) + 15
      }

      // 视口裁剪 - 临时禁用以解决节点消失问题
      const inViewport = isInViewport(node.x, node.y, 20)
      graphic.visible = true // 临时禁用视口裁剪，始终显示节点
      if (label) {
        label.visible = inViewport && !isPerformanceMode // 标签仍然使用视口裁剪以提高性能
      }

      // LOD 渲染
      if (inViewport) {
        const centerX = app.screen.width / 2
        const centerY = app.screen.height / 2
        const distance = Math.sqrt((node.x - centerX) ** 2 + (node.y - centerY) ** 2)

        // 调试：监控第一个节点的状态
        if (nodeId === graphData.filteredNodes[0]?.id && window.tickCount <= 10) {
          console.log(`🔍 节点 ${nodeId} 状态:`, {
            position: `(${node.x.toFixed(1)}, ${node.y.toFixed(1)})`,
            distance: distance.toFixed(1),
            inViewport,
            isPerformanceMode,
            visibleBefore: graphic.visible,
          })
        }

        applyLOD(graphic, distance)

        // 调试：检查LOD后的状态
        if (nodeId === graphData.filteredNodes[0]?.id && window.tickCount <= 10) {
          console.log(`🔍 节点 ${nodeId} LOD后:`, {
            visibleAfter: graphic.visible,
            alpha: graphic.alpha,
          })
        }
      }
    }
  })

  // 更新边位置
  let edgeUpdateCount = 0
  let visibleEdgeCount = 0
  let foundEdgeCount = 0

  // 添加tick计数器用于调试
  if (!window.tickCount) window.tickCount = 0
  window.tickCount++

  const simulationLinks = simulation.force('link').links()

  edgeGraphics.forEach((graphic, edgeId) => {
    const edge = simulationLinks.find((l) => l.id === edgeId)

    edgeUpdateCount++

    if (edge && graphic) {
      foundEdgeCount++
      // 确保 source 和 target 有坐标
      if (
        edge.source &&
        edge.target &&
        typeof edge.source.x === 'number' &&
        typeof edge.source.y === 'number' &&
        typeof edge.target.x === 'number' &&
        typeof edge.target.y === 'number'
      ) {
        // 检查边的两个端点是否在视口内
        const sourceInViewport = isInViewport(edge.source.x, edge.source.y)
        const targetInViewport = isInViewport(edge.target.x, edge.target.y)

        // 临时禁用视口裁剪，强制显示所有边
        if (true) {
          // 原来是: if (sourceInViewport || targetInViewport) {

          // 保存交互属性（因为 clear() 会重置它们）
          const edgeData = graphic.edgeData

          graphic.clear()

          // 确保交互属性始终正确设置
          graphic.eventMode = 'static'
          graphic.interactive = true
          graphic.buttonMode = true
          graphic.cursor = 'pointer'
          graphic.edgeData = edgeData

          // 移除所有旧的事件监听器，避免重复绑定
          graphic.removeAllListeners()

          // 注释掉边图形的直接事件监听器，改用全局检测
          // if (edgeData) {
          //   graphic.on('pointerdown', () => {
          //     console.log(`🔗 边 ${edgeData.id} pointerdown 事件被触发!`)
          //     handleEdgeClick(edgeData)
          //   })
          //   graphic.on('pointerover', () => {
          //     console.log(`🔗 边 ${edgeData.id} pointerover 事件被触发!`)
          //     handleEdgeHover(edgeData)
          //   })
          //   graphic.on('pointerout', () => {
          //     console.log(`🔗 边 ${edgeData.id} pointerout 事件被触发!`)
          //     handleEdgeOut(edgeData)
          //   })
          // }

          // 调试：确认事件监听器重新绑定（显示所有边的前5条）
          if (visibleEdgeCount < 5) {
            console.log(`🔗 边 ${edgeData.id} 事件监听器重新绑定:`, {
              eventMode: graphic.eventMode,
              cursor: graphic.cursor,
              interactive: graphic.interactive,
              buttonMode: graphic.buttonMode,
              hasPointerOver: graphic.listenerCount('pointerover') > 0,
              hasPointerOut: graphic.listenerCount('pointerout') > 0,
              hasPointerDown: graphic.listenerCount('pointerdown') > 0,
              position: `(${edge.source.x.toFixed(1)}, ${edge.source.y.toFixed(1)}) -> (${edge.target.x.toFixed(1)}, ${edge.target.y.toFixed(1)})`,
              visible: graphic.visible,
              alpha: graphic.alpha,
            })
          }

          // 调试：检查最终状态（只显示第一条边）
          if (edgeData.id === 'e6a55d44-5eb6-4daa-a1dc-58dc567e3436') {
            console.log(`🔗 边 ${edgeData.id} 绘制后最终交互状态:`, {
              eventMode: graphic.eventMode,
              cursor: graphic.cursor,
              hasPointerOverListener: graphic.listenerCount('pointerover') > 0,
              hasPointerOutListener: graphic.listenerCount('pointerout') > 0,
              hasPointerDownListener: graphic.listenerCount('pointerdown') > 0,
              edgeData: graphic.edgeData ? 'has data' : 'no data',
            })
          }

          // 先绘制一个不可见的粗线用于交互
          graphic.lineStyle(30, 0xffffff, 0.0) // 30像素宽，完全透明的交互区域
          graphic.moveTo(edge.source.x, edge.source.y)
          graphic.lineTo(edge.target.x, edge.target.y)

          // 然后绘制可见的细边
          graphic.lineStyle(2, 0x4ecdc4, isPerformanceMode ? 0.8 : 1.0) // 2像素宽的可见边，青色
          graphic.moveTo(edge.source.x, edge.source.y)
          graphic.lineTo(edge.target.x, edge.target.y)

          // 为前3条边添加额外的可见标记（圆圈）
          if (visibleEdgeCount < 3) {
            graphic.beginFill(0xff00ff, 1.0) // 紫色圆圈
            graphic.drawCircle(edge.source.x, edge.source.y, 10) // 起点圆圈
            graphic.drawCircle(edge.target.x, edge.target.y, 10) // 终点圆圈
            graphic.endFill()
          }

          graphic.visible = true
          visibleEdgeCount++

          // 详细的绘制调试
          if (visibleEdgeCount <= 3) {
            console.log(`边 ${edge.id} 详细绘制调试:`, {
              beforeClear: graphic.geometry ? graphic.geometry.graphicsData.length : 'no geometry',
              afterMoveTo: 'moveTo called',
              afterLineTo: 'lineTo called',
              graphicBounds: graphic.getBounds(),
              hasGeometry: !!graphic.geometry,
              geometryData: graphic.geometry ? graphic.geometry.graphicsData.length : 0,
              renderingProps: {
                visible: graphic.visible,
                alpha: graphic.alpha,
                worldVisible: graphic.worldVisible,
                renderable: graphic.renderable,
                mask: !!graphic.mask,
                filters: graphic.filters ? graphic.filters.length : 0,
                parent: !!graphic.parent,
                parentVisible: graphic.parent ? graphic.parent.visible : 'no parent',
              },
            })
          }

          // 调试前几条边的绘制信息
          if (visibleEdgeCount <= 3) {
            console.log(`边 ${edge.id} 绘制信息:`, {
              sourceCoords: `(${edge.source.x}, ${edge.source.y})`,
              targetCoords: `(${edge.target.x}, ${edge.target.y})`,
              sourceInViewport,
              targetInViewport,
              graphicVisible: graphic.visible,
              graphicParent: !!graphic.parent,
              graphicPosition: `(${graphic.x}, ${graphic.y})`,
              graphicAlpha: graphic.alpha,
              viewportBounds: {
                x: viewportBounds.x,
                y: viewportBounds.y,
                width: viewportBounds.width,
                height: viewportBounds.height,
              },
              viewport: {
                x: viewport.x,
                y: viewport.y,
                scale: viewport.scale,
              },
              stageTransform: {
                x: app.stage.position.x,
                y: app.stage.position.y,
                scaleX: app.stage.scale.x,
                scaleY: app.stage.scale.y,
              },
            })
          }
        } else {
          graphic.visible = false
        }
      } else {
        graphic.visible = false
      }
    } else {
      // 边没有找到或图形对象无效
      if (edgeUpdateCount <= 3) {
        // 只在前几次打印
        console.log(`边 ${edgeId} 未找到或图形无效:`, {
          hasEdge: !!edge,
          hasGraphic: !!graphic,
          edgeId,
        })
      }
    }
  })

  // 调试信息（只在前几次 tick 中打印）
  if (window.tickCount <= 5) {
    console.log('Tick 调试信息:', {
      tickCount: window.tickCount,
      edgeGraphicsSize: edgeGraphics.size,
      edgeUpdateCount,
      foundEdgeCount,
      visibleEdgeCount,
      simulationLinksCount: simulation.force('link').links().length,
      alpha: simulation.alpha(),
      edgesContainerInfo: {
        visible: edgesContainer.visible,
        alpha: edgesContainer.alpha,
        childrenCount: edgesContainer.children.length,
        position: `(${edgesContainer.x}, ${edgesContainer.y})`,
        scale: `(${edgesContainer.scale.x}, ${edgesContainer.scale.y})`,
      },
      firstSimulationLink: simulationLinks[0]
        ? {
            id: simulationLinks[0].id,
            hasSource: !!simulationLinks[0].source,
            hasTarget: !!simulationLinks[0].target,
            sourceCoords: simulationLinks[0].source
              ? `(${simulationLinks[0].source.x}, ${simulationLinks[0].source.y})`
              : 'none',
            targetCoords: simulationLinks[0].target
              ? `(${simulationLinks[0].target.x}, ${simulationLinks[0].target.y})`
              : 'none',
          }
        : 'none',
    })
  }

  // 最终确保所有边的交互属性正确设置
  edgeGraphics.forEach((graphic, edgeData) => {
    if (graphic && graphic.edgeData) {
      graphic.eventMode = 'static'
      graphic.cursor = 'pointer'

      // 调试：确认最终设置（只显示前3条边）
      if (visibleEdgeCount < 3) {
        console.log(`🔗 边 ${graphic.edgeData.id} 最终交互属性设置:`, {
          eventMode: graphic.eventMode,
          cursor: graphic.cursor,
          hasPointerOverListener: graphic.listenerCount('pointerover') > 0,
          hasPointerOutListener: graphic.listenerCount('pointerout') > 0,
          hasPointerDownListener: graphic.listenerCount('pointerdown') > 0,
        })
      }
    }
  })

  // 更新物理引擎状态
  uiState.setPhysicsAlpha(simulation.alpha())

  // 更新性能指标
  updatePerformanceMetrics()
}

/**
 * 创建节点图形
 */
function createNodeGraphic(node) {
  const graphic = getNodeFromPool()

  // 计算节点大小和颜色
  const radius = Math.sqrt(node.degree) * 2 + 5
  const color = getNodeColor(node.degree)

  // 绘制节点
  graphic.beginFill(color, 0.8)
  graphic.drawCircle(0, 0, radius)
  graphic.endFill()

  // 添加边框（重要节点）
  if (node.degree > 10) {
    graphic.lineStyle(2, 0xffffff, 0.8)
    graphic.drawCircle(0, 0, radius)

    // 添加光晕效果
    addNodeGlow(graphic, color, node.degree > 20 ? 2 : 1)
  }

  // 设置交互
  graphic.eventMode = 'static'
  graphic.cursor = 'pointer'
  graphic.nodeData = node // 存储节点数据引用

  // 添加事件监听器
  graphic.on('pointerdown', () => handleNodeClick(node))
  graphic.on('pointerover', () => handleNodeHover(node))
  graphic.on('pointerout', () => handleNodeOut(node))

  // 添加选中状态动画
  if (uiState.selectedNode && uiState.selectedNode.id === node.id) {
    addSelectionAnimation(graphic)
  }

  return graphic
}

/**
 * 创建节点标签
 */
function createNodeLabel(node) {
  const text = new PIXI.Text(node.title || node.id, {
    fontFamily: 'Arial, sans-serif',
    fontSize: Math.max(10, Math.min(16, node.degree / 2 + 8)),
    fill: 0xffffff,
    align: 'center',
    stroke: 0x000000,
    strokeThickness: 2,
  })

  text.anchor.set(0.5)
  text.x = node.x || 0
  text.y = (node.y || 0) + getNodeRadius(node.degree) + 15

  return text
}

/**
 * 获取节点半径
 */
function getNodeRadius(degree) {
  return Math.sqrt(degree) * 2 + 5
}

/**
 * 获取节点颜色
 */
function getNodeColor(degree) {
  if (degree > 20) return 0xff6b6b // 超级节点
  if (degree > 10) return 0x4ecdc4 // 重要节点
  if (degree > 5) return 0x45b7d1 // 活跃节点
  if (degree > 2) return 0x96ceb4 // 普通节点
  if (degree > 0) return 0xffeaa7 // 边缘节点
  return 0xdda0dd // 孤立节点
}

/**
 * 处理节点点击
 */
function handleNodeClick(node) {
  uiState.selectNode(node)

  // 添加点击粒子效果
  const graphic = nodeGraphics.get(node.id)
  if (graphic) {
    const color = getNodeColor(node.degree)
    createParticleEffect(graphic.x, graphic.y, color)
  }
}

/**
 * 处理节点悬停
 */
function handleNodeHover(node) {
  console.log('🎯 节点悬停事件触发:', node)
  uiState.hoverNode(node)

  // 清除之前的定时器
  if (tooltipTimeout) {
    clearTimeout(tooltipTimeout)
  }
  if (hoverDetailTimeout) {
    clearTimeout(hoverDetailTimeout)
  }
  if (hideDetailTimeout) {
    clearTimeout(hideDetailTimeout)
  }

  // 延迟显示工具提示
  tooltipTimeout = setTimeout(() => {
    const tooltipContent = `${node.title || node.id}\n度数: ${node.degree}\n类型: ${node.type || '未分类'}`
    console.log('💬 显示工具提示:', tooltipContent)
    uiState.showTooltip(0, 0, tooltipContent)
  }, 500)

  // 延迟0.5秒显示悬停详情面板
  hoverDetailTimeout = setTimeout(() => {
    console.log('📋 显示悬停详情面板:', node)
    uiState.showHoverNodeDetail(node)
  }, 500)

  // 高亮相关节点和边
  highlightConnectedElements(node)
}

/**
 * 处理节点移出
 */
function handleNodeOut(node) {
  // 清除工具提示定时器
  if (tooltipTimeout) {
    clearTimeout(tooltipTimeout)
    tooltipTimeout = null
  }

  // 清除悬停详情定时器
  if (hoverDetailTimeout) {
    clearTimeout(hoverDetailTimeout)
    hoverDetailTimeout = null
  }

  // 延迟隐藏工具提示
  setTimeout(() => {
    uiState.clearHover()
    uiState.hideTooltip()
  }, 2000)

  // 延迟2秒隐藏悬停详情面板
  hideDetailTimeout = setTimeout(() => {
    uiState.hideHoverDetailPanel()
  }, 2000)

  // 清除高亮
  clearHighlight()
}

// 悬停详情面板定时器
let hoverDetailTimeout = null
let hideDetailTimeout = null

/**
 * 处理边点击
 */
function handleEdgeClick(edge) {
  uiState.selectEdge(edge)
}

/**
 * 全局鼠标移动事件处理 - 用于检测边悬停
 */
let currentHoveredEdge = null
let currentGlobalHoveredNode = null // 跟踪全局悬停的节点
let lastMousePosition = { x: 0, y: 0 }

function handleGlobalPointerMove(event) {
  const mouseX = event.global.x
  const mouseY = event.global.y
  lastMousePosition = { x: mouseX, y: mouseY }

  // 首先检测鼠标是否在节点区域内
  const hoveredNode = findNodeAtPosition(mouseX, mouseY)

  // 如果鼠标在节点区域内，完全忽略边的处理
  if (hoveredNode) {
    // 如果之前有悬停的边，清除边悬停状态
    if (currentHoveredEdge) {
      console.log(`🔗 鼠标进入节点区域，清除边悬停: ${currentHoveredEdge.id}`)
      handleEdgeOut(currentHoveredEdge)
      currentHoveredEdge = null
    }

    // 只有当悬停的节点发生变化时，才触发节点悬停逻辑
    if (hoveredNode !== currentGlobalHoveredNode) {
      console.log(`🎯 全局检测到新节点悬停，主动触发节点悬停逻辑: ${hoveredNode.id}`)
      handleNodeHover(hoveredNode)
      currentGlobalHoveredNode = hoveredNode
    }

    // 调试：每100次移动打印一次检测结果
    if (!window.globalMoveCount) window.globalMoveCount = 0
    window.globalMoveCount++
    if (window.globalMoveCount % 100 === 0) {
      console.log(`🔍 全局鼠标检测 (${window.globalMoveCount}) - 节点区域:`, {
        mousePos: `(${mouseX.toFixed(1)}, ${mouseY.toFixed(1)})`,
        hoveredNode: hoveredNode.id,
        message: '在节点区域内，忽略边检测',
      })
    }
    return // 直接返回，不进行边检测
  }

  // 如果鼠标离开了节点区域，清除全局节点悬停状态
  if (currentGlobalHoveredNode) {
    console.log(`🎯 鼠标离开节点区域: ${currentGlobalHoveredNode.id}`)
    currentGlobalHoveredNode = null
  }

  // 只有当鼠标不在任何节点区域内时，才检测边悬停
  const hoveredEdge = findEdgeAtPosition(mouseX, mouseY)

  // 调试：每100次移动打印一次检测结果
  if (!window.globalMoveCount) window.globalMoveCount = 0
  window.globalMoveCount++
  if (window.globalMoveCount % 100 === 0) {
    console.log(`🔍 全局鼠标检测 (${window.globalMoveCount}) - 边检测区域:`, {
      mousePos: `(${mouseX.toFixed(1)}, ${mouseY.toFixed(1)})`,
      hoveredEdge: hoveredEdge ? hoveredEdge.id : 'none',
      currentHoveredEdge: currentHoveredEdge ? currentHoveredEdge.id : 'none',
      edgeCount: graphData.filteredEdges.length,
    })
  }

  if (hoveredEdge !== currentHoveredEdge) {
    // 边悬停状态改变
    if (currentHoveredEdge) {
      // 离开之前的边
      console.log(`🔗 离开边: ${currentHoveredEdge.id}`)
      handleEdgeOut(currentHoveredEdge)
    }

    if (hoveredEdge) {
      // 进入新的边
      console.log(`🔗 全局检测到边悬停: ${hoveredEdge.id}`)
      handleEdgeHover(hoveredEdge)
    }

    currentHoveredEdge = hoveredEdge
  }
}

/**
 * 全局鼠标点击事件处理 - 用于检测边点击
 */
function handleGlobalPointerDown(event) {
  const mouseX = event.global.x
  const mouseY = event.global.y

  // 首先检测鼠标是否在节点区域内
  const clickedNode = findNodeAtPosition(mouseX, mouseY)

  // 如果鼠标在节点区域内，完全忽略边的点击处理
  if (clickedNode) {
    console.log(`🎯 鼠标在节点区域内点击，忽略边点击检测: ${clickedNode.id}`)
    return // 直接返回，不进行边点击检测
  }

  // 只有当鼠标不在任何节点区域内时，才检测边点击
  const clickedEdge = findEdgeAtPosition(mouseX, mouseY)

  if (clickedEdge) {
    console.log(`🔗 全局检测到边点击: ${clickedEdge.id}`)
    handleEdgeClick(clickedEdge)
  }
}

/**
 * 查找指定位置的节点
 * @param {number} screenX - 屏幕坐标X
 * @param {number} screenY - 屏幕坐标Y
 * @returns {Object|null} - 找到的节点对象或null
 */
function findNodeAtPosition(screenX, screenY) {
  // 将屏幕坐标转换为世界坐标
  const worldX = (screenX - viewport.x) / viewport.scale
  const worldY = (screenY - viewport.y) / viewport.scale

  for (const node of simulation.nodes()) {
    if (typeof node.x === 'number' && typeof node.y === 'number') {
      // 计算鼠标到节点中心的距离
      const distance = Math.sqrt((worldX - node.x) ** 2 + (worldY - node.y) ** 2)

      // 获取节点半径
      const nodeRadius = getNodeRadius(node.degree)

      // 如果鼠标在节点半径内，返回该节点
      if (distance <= nodeRadius) {
        return node
      }
    }
  }

  return null
}

/**
 * 查找指定位置的边
 * @param {number} screenX - 屏幕坐标X
 * @param {number} screenY - 屏幕坐标Y
 * @returns {Object|null} - 找到的边对象或null
 */
function findEdgeAtPosition(screenX, screenY) {
  const threshold = 15 // 检测阈值，像素

  // 将屏幕坐标转换为世界坐标
  const worldX = (screenX - viewport.x) / viewport.scale
  const worldY = (screenY - viewport.y) / viewport.scale

  for (const edge of graphData.filteredEdges) {
    const sourceNode = simulation.nodes().find((n) => n.id === edge.source)
    const targetNode = simulation.nodes().find((n) => n.id === edge.target)

    if (
      sourceNode &&
      targetNode &&
      typeof sourceNode.x === 'number' &&
      typeof sourceNode.y === 'number' &&
      typeof targetNode.x === 'number' &&
      typeof targetNode.y === 'number'
    ) {
      // 计算点到线段的距离（使用世界坐标）
      const distance = distanceToLineSegment(
        worldX,
        worldY,
        sourceNode.x,
        sourceNode.y,
        targetNode.x,
        targetNode.y,
      )

      if (distance <= threshold) {
        return edge
      }
    }
  }

  return null
}

/**
 * 计算点到线段的距离
 */
function distanceToLineSegment(px, py, x1, y1, x2, y2) {
  const A = px - x1
  const B = py - y1
  const C = x2 - x1
  const D = y2 - y1

  const dot = A * C + B * D
  const lenSq = C * C + D * D

  if (lenSq === 0) {
    // 线段长度为0，返回点到点的距离
    return Math.sqrt(A * A + B * B)
  }

  let param = dot / lenSq

  let xx, yy

  if (param < 0) {
    xx = x1
    yy = y1
  } else if (param > 1) {
    xx = x2
    yy = y2
  } else {
    xx = x1 + param * C
    yy = y1 + param * D
  }

  const dx = px - xx
  const dy = py - yy
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * 处理边悬停
 */
function handleEdgeHover(edge) {
  console.log('🔗 边悬停事件触发:', edge)
  uiState.hoverEdge(edge)

  // 清除之前的定时器
  if (hoverDetailTimeout) {
    clearTimeout(hoverDetailTimeout)
  }
  if (hideDetailTimeout) {
    clearTimeout(hideDetailTimeout)
  }

  // 延迟0.5秒显示详情面板
  hoverDetailTimeout = setTimeout(() => {
    console.log('📋 显示边悬停详情面板:', edge)
    uiState.showHoverEdgeDetail(edge)
  }, 500)
}

/**
 * 处理边移出
 */
function handleEdgeOut(edge) {
  // 清除悬停定时器
  if (hoverDetailTimeout) {
    clearTimeout(hoverDetailTimeout)
    hoverDetailTimeout = null
  }

  // 延迟2秒隐藏详情面板
  hideDetailTimeout = setTimeout(() => {
    uiState.hideHoverDetailPanel()
  }, 2000)
}

/**
 * 高亮连接的元素
 */
function highlightConnectedElements(node) {
  // 重置所有节点和边的透明度
  nodeGraphics.forEach((graphic) => {
    graphic.alpha = 0.3
  })
  edgeGraphics.forEach((graphic) => {
    graphic.alpha = 0.1
  })

  // 高亮当前节点
  const currentNodeGraphic = nodeGraphics.get(node.id)
  if (currentNodeGraphic) {
    currentNodeGraphic.alpha = 1.0
    // 添加光晕效果
    try {
      if (PIXI.filters && PIXI.filters.GlowFilter) {
        currentNodeGraphic.filters = [
          new PIXI.filters.GlowFilter({ color: 0xffffff, outerStrength: 2 }),
        ]
      }
    } catch (error) {
      console.warn('高亮效果创建失败:', error)
    }
  }

  // 高亮连接的边和节点
  graphData.filteredEdges.forEach((edge) => {
    if (edge.source === node.id || edge.target === node.id) {
      const edgeGraphic = edgeGraphics.get(edge.id)
      if (edgeGraphic) {
        edgeGraphic.alpha = 0.8
      }

      // 高亮连接的节点
      const connectedNodeId = edge.source === node.id ? edge.target : edge.source
      const connectedNodeGraphic = nodeGraphics.get(connectedNodeId)
      if (connectedNodeGraphic) {
        connectedNodeGraphic.alpha = 0.7
      }
    }
  })
}

/**
 * 清除高亮
 */
function clearHighlight() {
  nodeGraphics.forEach((graphic) => {
    graphic.alpha = 1.0
    graphic.filters = []
  })
  edgeGraphics.forEach((graphic) => {
    graphic.alpha = 0.3
  })
}

/**
 * 添加选中动画
 */
function addSelectionAnimation(graphic) {
  // 脉冲动画
  const ticker = new PIXI.Ticker()
  let time = 0

  ticker.add(() => {
    time += ticker.deltaTime * 0.1
    const scale = 1 + Math.sin(time) * 0.2
    graphic.scale.set(scale)
  })

  ticker.start()

  // 存储ticker以便后续清理
  graphic.selectionTicker = ticker
}

/**
 * 移除选中动画
 */
function removeSelectionAnimation(graphic) {
  if (graphic.selectionTicker) {
    graphic.selectionTicker.stop()
    graphic.selectionTicker.destroy()
    graphic.selectionTicker = null
    graphic.scale.set(1)
  }
}

/**
 * 重置视图到中心
 */
function resetView() {
  viewport.x = 0
  viewport.y = 0
  viewport.scale = 1
  updateViewTransform()
  uiState.updateViewTransform(viewport)
}

/**
 * 调整视图以显示所有节点
 */
function fitToNodes() {
  const nodes = simulation.nodes()
  if (nodes.length === 0) return

  // 计算所有节点的边界
  let minX = Infinity,
    maxX = -Infinity
  let minY = Infinity,
    maxY = -Infinity

  nodes.forEach((node) => {
    if (typeof node.x === 'number' && typeof node.y === 'number') {
      minX = Math.min(minX, node.x)
      maxX = Math.max(maxX, node.x)
      minY = Math.min(minY, node.y)
      maxY = Math.max(maxY, node.y)
    }
  })

  if (minX === Infinity) return

  // 计算中心点
  const centerX = (minX + maxX) / 2
  const centerY = (minY + maxY) / 2

  // 计算需要的缩放比例
  const width = maxX - minX
  const height = maxY - minY
  const padding = 100

  const scaleX = (app.screen.width - padding * 2) / width
  const scaleY = (app.screen.height - padding * 2) / height
  const scale = Math.min(scaleX, scaleY, 1) // 不要放大超过1倍

  // 设置视口
  viewport.scale = scale
  viewport.x = app.screen.width / 2 - centerX * scale
  viewport.y = app.screen.height / 2 - centerY * scale

  updateViewTransform()
  uiState.updateViewTransform(viewport)

  console.log('调整视图以显示所有节点:', {
    bounds: { minX, maxX, minY, maxY },
    center: { centerX, centerY },
    viewport: { x: viewport.x, y: viewport.y, scale: viewport.scale },
  })
}

/**
 * 创建星空背景
 */
function createStarField() {
  if (!backgroundContainer) return

  const starCount = 200
  const width = app.screen.width
  const height = app.screen.height

  for (let i = 0; i < starCount; i++) {
    const star = new PIXI.Graphics()
    const size = Math.random() * 2 + 0.5
    const alpha = Math.random() * 0.8 + 0.2
    const color = Math.random() > 0.7 ? 0x4ecdc4 : 0xffffff

    star.beginFill(color, alpha)
    star.drawCircle(0, 0, size)
    star.endFill()

    star.x = Math.random() * width
    star.y = Math.random() * height

    // 添加闪烁动画
    const ticker = new PIXI.Ticker()
    let time = Math.random() * Math.PI * 2

    ticker.add(() => {
      time += ticker.deltaTime * 0.02
      star.alpha = alpha * (0.5 + 0.5 * Math.sin(time))
    })

    ticker.start()
    star.twinkleTicker = ticker

    backgroundContainer.addChild(star)
  }
}

/**
 * 创建节点光晕效果
 */
function addNodeGlow(graphic, color, intensity = 1) {
  try {
    // 检查 GlowFilter 是否可用
    if (PIXI.filters && PIXI.filters.GlowFilter) {
      const glowFilter = new PIXI.filters.GlowFilter({
        color: color,
        outerStrength: intensity * 2,
        innerStrength: intensity,
        distance: 10,
        quality: 0.5,
      })

      graphic.filters = [glowFilter]
    } else {
      // 如果 GlowFilter 不可用，使用简单的边框效果
      console.warn('GlowFilter 不可用，使用边框效果替代')
    }
  } catch (error) {
    console.warn('创建光晕效果失败:', error)
    // 降级到简单边框，不做任何操作
  }
}

/**
 * 创建粒子效果
 */
function createParticleEffect(x, y, color) {
  // 性能模式下减少粒子数量
  const particleCount = isPerformanceMode ? 5 : 10

  for (let i = 0; i < particleCount; i++) {
    const particle = new PIXI.Graphics()
    particle.beginFill(color, 0.8)
    particle.drawCircle(0, 0, Math.random() * 3 + 1)
    particle.endFill()

    particle.x = x
    particle.y = y

    const angle = (Math.PI * 2 * i) / particleCount
    const speed = Math.random() * 50 + 20
    const vx = Math.cos(angle) * speed
    const vy = Math.sin(angle) * speed

    nodesContainer.addChild(particle)

    // 粒子动画
    const ticker = new PIXI.Ticker()
    let life = 1.0

    ticker.add(() => {
      particle.x += vx * ticker.deltaTime * 0.1
      particle.y += vy * ticker.deltaTime * 0.1
      life -= ticker.deltaTime * 0.05
      particle.alpha = life

      if (life <= 0) {
        ticker.stop()
        ticker.destroy()
        nodesContainer.removeChild(particle)
      }
    })

    ticker.start()
  }
}

/**
 * 更新视口边界
 */
function updateViewportBounds() {
  const padding = 500 // 增加边界填充，让节点在更远的地方才被裁剪
  viewportBounds = {
    x: -viewport.x / viewport.scale - padding,
    y: -viewport.y / viewport.scale - padding,
    width: app.screen.width / viewport.scale + padding * 2,
    height: app.screen.height / viewport.scale + padding * 2,
  }
}

/**
 * 检查对象是否在视口内
 */
function isInViewport(x, y, radius = 0) {
  return (
    x + radius >= viewportBounds.x &&
    x - radius <= viewportBounds.x + viewportBounds.width &&
    y + radius >= viewportBounds.y &&
    y - radius <= viewportBounds.y + viewportBounds.height
  )
}

/**
 * 从对象池获取节点图形
 */
function getNodeFromPool() {
  return nodePool.pop() || new PIXI.Graphics()
}

/**
 * 将节点图形返回对象池
 */
function returnNodeToPool(graphic) {
  graphic.clear()
  graphic.removeAllListeners()
  graphic.filters = []
  graphic.alpha = 1
  graphic.scale.set(1)
  graphic.visible = true
  nodePool.push(graphic)
}

/**
 * 从对象池获取边图形
 */
function getEdgeFromPool() {
  return edgePool.pop() || new PIXI.Graphics()
}

/**
 * 将边图形返回对象池
 */
function returnEdgeToPool(graphic) {
  graphic.clear()
  edgePool.push(graphic)
}

/**
 * 性能监控
 */
function updatePerformanceMetrics() {
  const now = performance.now()
  frameCount++

  if (now - lastFrameTime >= 1000) {
    const fps = (frameCount * 1000) / (now - lastFrameTime)
    const frameTime = (now - lastFrameTime) / frameCount

    // 更新性能数据
    uiState.updatePerformance({
      fps,
      frameTime,
      nodeCount: nodeGraphics.size,
      edgeCount: edgeGraphics.size,
      memoryUsage: performance.memory ? performance.memory.usedJSHeapSize : 0,
    })

    // 根据性能调整渲染质量 - 临时禁用自动性能模式
    if (fps < 30 && !isPerformanceMode) {
      // isPerformanceMode = true // 临时禁用自动性能模式
      console.log('FPS低但未启用性能模式 (已禁用自动切换)')
    } else if (fps > 50 && isPerformanceMode) {
      isPerformanceMode = false
      console.log('关闭性能模式')
    }

    frameCount = 0
    lastFrameTime = now
  }
}

/**
 * LOD (Level of Detail) 渲染
 */
function applyLOD(graphic, distance) {
  if (isPerformanceMode) {
    // 距离较远时简化渲染，但不隐藏节点
    if (distance > 500) {
      // graphic.visible = false // 禁用距离隐藏，避免节点消失
      graphic.alpha = 0.3 // 改为降低透明度
      graphic.filters = [] // 移除滤镜效果
    } else if (distance > 200) {
      graphic.alpha = 0.5
      // 移除复杂的滤镜效果
      graphic.filters = []
    } else {
      // graphic.visible = true // 不需要设置，因为我们已经在上面设置了
      graphic.alpha = 1.0
    }
  }
}

/**
 * 更新图谱数据
 */
// 防止重复调用的标志
let isUpdatingGraphData = false

function updateGraphData() {
  if (isUpdatingGraphData) {
    console.log('updateGraphData 已在执行中，跳过此次调用')
    return
  }

  isUpdatingGraphData = true

  if (!window.updateGraphDataCallCount) window.updateGraphDataCallCount = 0
  window.updateGraphDataCallCount++

  console.log('updateGraphData 被调用 (第' + window.updateGraphDataCallCount + '次):', {
    hasApp: !!app,
    hasSimulation: !!simulation,
    nodesLength: graphData.filteredNodes.length,
    edgesLength: graphData.filteredEdges.length,
    currentEdgeGraphicsSize: edgeGraphics.size,
  })

  // 移除调试调用栈

  if (!app || !simulation) {
    console.warn('app 或 simulation 未初始化')
    isUpdatingGraphData = false
    return
  }

  // 清除现有图形并返回对象池
  nodeGraphics.forEach((graphic) => {
    nodesContainer.removeChild(graphic)
    returnNodeToPool(graphic)
  })
  nodeLabels.forEach((label) => {
    nodesContainer.removeChild(label)
  })
  edgeGraphics.forEach((graphic) => {
    edgesContainer.removeChild(graphic)
    returnEdgeToPool(graphic)
  })

  console.log('清空图形前:', {
    nodeGraphicsSize: nodeGraphics.size,
    edgeGraphicsSize: edgeGraphics.size,
    callCount: window.updateGraphDataCallCount,
  })

  // 暂停模拟，防止在数据更新过程中触发tick
  const wasRunning = simulation.alpha() > 0
  simulation.stop()

  nodeGraphics.clear()
  nodeLabels.clear()
  edgeGraphics.clear()

  console.log('清空图形后:', {
    nodeGraphicsSize: nodeGraphics.size,
    edgeGraphicsSize: edgeGraphics.size,
    simulationStopped: simulation.alpha() === 0,
  })

  // 创建节点图形和标签
  console.log('开始创建节点图形，节点数量:', graphData.filteredNodes.length)
  graphData.filteredNodes.forEach((node, index) => {
    if (index < 3) {
      // 只打印前3个节点的详细信息
      console.log(`创建节点 ${index}:`, node)
    }

    const graphic = createNodeGraphic(node)
    const label = createNodeLabel(node)

    nodeGraphics.set(node.id, graphic)
    nodeLabels.set(node.id, label)

    nodesContainer.addChild(graphic)
    nodesContainer.addChild(label)
  })
  console.log('节点图形创建完成，总数:', nodeGraphics.size)

  // 创建边图形
  console.log('开始创建边图形，边数量:', graphData.filteredEdges.length)
  console.log('创建前 edgeGraphics.size:', edgeGraphics.size)

  graphData.filteredEdges.forEach((edge, index) => {
    if (index < 3) {
      // 只打印前3条边的详细信息
      console.log(`创建边 ${index}:`, edge)
    }

    const graphic = getEdgeFromPool()
    if (!graphic) {
      console.error(`边 ${index} 获取图形失败`)
      return
    }

    // 调试：边图形获取成功
    if (index === 0) {
      console.log(`🔗 边 ${index} 图形获取成功，开始设置交互`)
    }

    // 设置边的交互
    graphic.eventMode = 'static'
    graphic.interactive = true
    graphic.buttonMode = true
    graphic.cursor = 'pointer'
    graphic.edgeData = edge // 存储边数据引用

    // 调试：交互属性设置后
    if (index === 0) {
      console.log(`🔗 边 ${index} 交互属性设置后:`, {
        eventMode: graphic.eventMode,
        cursor: graphic.cursor,
        edgeData: graphic.edgeData ? 'has data' : 'no data',
      })
    }

    // 注释掉边图形的直接事件监听器，改用全局检测
    // graphic.on('pointerdown', () => {
    //   console.log(`🔗 边 ${edge.id} 初始 pointerdown 事件被触发!`)
    //   handleEdgeClick(edge)
    // })
    // graphic.on('pointerover', () => {
    //   console.log(`🔗 边 ${edge.id} 初始 pointerover 事件被触发!`)
    //   handleEdgeHover(edge)
    // })
    // graphic.on('pointerout', () => {
    //   console.log(`🔗 边 ${edge.id} 初始 pointerout 事件被触发!`)
    //   handleEdgeOut(edge)
    // })

    // 调试：事件监听器添加后（显示前5条边）
    if (index < 5) {
      console.log(`🔗 边 ${edge.id} 初始交互设置:`, {
        eventMode: graphic.eventMode,
        cursor: graphic.cursor,
        interactive: graphic.interactive,
        buttonMode: graphic.buttonMode,
        hasPointerOverListener: graphic.listenerCount('pointerover') > 0,
        hasPointerOutListener: graphic.listenerCount('pointerout') > 0,
        hasPointerDownListener: graphic.listenerCount('pointerdown') > 0,
      })
    }

    edgeGraphics.set(edge.id, graphic)
    edgesContainer.addChild(graphic)

    if (index < 3) {
      console.log(`边 ${index} 创建后 edgeGraphics.size:`, edgeGraphics.size)
    }
  })
  console.log('边图形创建完成，总数:', edgeGraphics.size)

  // 立即检查边图形状态
  setTimeout(() => {
    console.log('1秒后边图形状态:', {
      edgeGraphicsSize: edgeGraphics.size,
      edgesContainerChildren: edgesContainer.children.length,
    })

    // 测试第一条边的交互功能
    if (edgeGraphics.size > 0) {
      const firstEdgeId = Array.from(edgeGraphics.keys())[0]
      const firstEdgeGraphic = edgeGraphics.get(firstEdgeId)
      const firstEdgeData = graphData.filteredEdges[0]

      console.log('🧪 测试第一条边的交互功能:', {
        edgeId: firstEdgeId,
        hasGraphic: !!firstEdgeGraphic,
        hasData: !!firstEdgeData,
        eventMode: firstEdgeGraphic?.eventMode,
        interactive: firstEdgeGraphic?.interactive,
        listenerCount: {
          pointerover: firstEdgeGraphic?.listenerCount('pointerover'),
          pointerout: firstEdgeGraphic?.listenerCount('pointerout'),
          pointerdown: firstEdgeGraphic?.listenerCount('pointerdown'),
        },
      })

      // 手动触发悬停事件进行测试
      if (firstEdgeGraphic && firstEdgeData) {
        console.log('🧪 手动触发边悬停事件测试...')
        setTimeout(() => {
          handleEdgeHover(firstEdgeData)
        }, 2000)

        // 测试事件监听器是否真的工作
        setTimeout(() => {
          console.log('🧪 尝试手动触发 pointerover 事件...')
          firstEdgeGraphic.emit('pointerover', { target: firstEdgeGraphic })
        }, 3000)
      }
    }
  }, 1000)

  // 更新模拟数据
  console.log('设置模拟节点前:', simulation.nodes().length)
  simulation.nodes(graphData.filteredNodes)
  console.log('设置模拟节点后:', simulation.nodes().length)

  // 为边创建正确的节点引用
  const nodeMap = new Map(graphData.filteredNodes.map((node) => [node.id, node]))
  const linksWithNodeRefs = graphData.filteredEdges.map((edge) => ({
    ...edge,
    source: nodeMap.get(edge.source) || edge.source,
    target: nodeMap.get(edge.target) || edge.target,
  }))

  console.log('设置模拟链接前:', simulation.force('link').links().length)
  simulation.force('link').links(linksWithNodeRefs)
  console.log('设置模拟链接后:', simulation.force('link').links().length)

  // 立即验证模拟数据是否正确设置
  const verifyNodes = simulation.nodes()
  const verifyLinks = simulation.force('link').links()
  console.log('模拟数据验证:', {
    nodeCount: verifyNodes.length,
    linkCount: verifyLinks.length,
    firstNode: verifyNodes[0]
      ? { id: verifyNodes[0].id, x: verifyNodes[0].x, y: verifyNodes[0].y }
      : 'no nodes',
    firstLink: verifyLinks[0]
      ? {
          id: verifyLinks[0].id,
          source: verifyLinks[0].source?.id,
          target: verifyLinks[0].target?.id,
        }
      : 'no links',
  })

  console.log('图谱数据更新完成:', {
    nodes: graphData.filteredNodes.length,
    edges: graphData.filteredEdges.length,
    linksWithRefs: linksWithNodeRefs.length,
  })

  console.log('物理状态检查:', {
    physicsRunning: uiState.physicsRunning,
    simulationAlpha: simulation.alpha(),
    willRestart: uiState.physicsRunning,
  })

  // 重启模拟（只有在物理模拟开启且之前在运行时才重启）
  if (uiState.physicsRunning && (wasRunning || simulation.alpha() === 0)) {
    console.log('重启前最后检查:', {
      simulationNodes: simulation.nodes().length,
      simulationLinks: simulation.force('link').links().length,
      wasRunning: wasRunning,
    })

    simulation.alpha(0.3).restart()

    console.log('模拟重启状态：', {
      alpha: simulation.alpha(),
      hasTickHandler: !!simulation.on('tick'),
      simulationNodes: simulation.nodes().length,
      simulationLinks: simulation.force('link').links().length,
      physicsRunning: uiState.physicsRunning,
    })

    // 立即再次检查
    setTimeout(() => {
      console.log('重启后100ms检查:', {
        simulationNodes: simulation.nodes().length,
        simulationLinks: simulation.force('link').links().length,
        alpha: simulation.alpha(),
      })
    }, 100)

    // 等待一点时间让节点有初始位置，然后调整视图
    setTimeout(() => {
      fitToNodes()
    }, 100)
  }

  // 重置标志
  isUpdatingGraphData = false
}

/**
 * 调整画布大小
 */
function resizeCanvas() {
  if (!app) return

  const width = window.innerWidth - uiState.sidebarWidth
  const height = window.innerHeight

  app.renderer.resize(width, height)
  app.stage.hitArea = new PIXI.Rectangle(0, 0, width, height)

  // 更新力导向中心
  if (simulation) {
    simulation.force('center', d3.forceCenter(width / 2, height / 2))
  }
}

// 监听数据变化 - 使用节点和边的长度来避免深度监听导致的无限循环
watch(() => [graphData.filteredNodes.length, graphData.filteredEdges.length], updateGraphData)
watch(() => graphData.filteredNodes.map((n) => n.id).join(','), updateGraphData)
watch(() => graphData.filteredEdges.map((e) => e.id).join(','), updateGraphData)

// 监听物理引擎状态
watch(
  () => uiState.physicsRunning,
  (running) => {
    if (!simulation) return

    if (running) {
      simulation.alpha(0.3).restart()
    } else {
      simulation.stop()
    }
  },
)

// 监听画布大小变化
watch(() => uiState.canvasSize, resizeCanvas, { deep: true })

// 监听选中状态变化
watch(
  () => uiState.selectedNode,
  (newNode, oldNode) => {
    // 移除旧节点的选中动画
    if (oldNode) {
      const oldGraphic = nodeGraphics.get(oldNode.id)
      if (oldGraphic) {
        removeSelectionAnimation(oldGraphic)
      }
    }

    // 添加新节点的选中动画
    if (newNode) {
      const newGraphic = nodeGraphics.get(newNode.id)
      if (newGraphic) {
        addSelectionAnimation(newGraphic)
      }
    }
  },
)

// 监听视图变换
watch(
  () => uiState.viewTransform,
  (transform) => {
    viewport.x = transform.x
    viewport.y = transform.y
    viewport.scale = transform.scale
    updateViewTransform()
  },
  { deep: true },
)

// 组件挂载
onMounted(() => {
  console.log('GraphCanvas 组件挂载开始')

  initPixiApp()
  initD3Simulation()

  console.log('初始化完成，检查数据:', {
    nodesLength: graphData.filteredNodes.length,
    edgesLength: graphData.filteredEdges.length,
    hasApp: !!app,
    hasSimulation: !!simulation,
  })

  // 初始数据更新
  if (graphData.filteredNodes.length > 0) {
    console.log('有初始数据，调用 updateGraphData')
    updateGraphData()
  } else {
    console.log('没有初始数据，等待数据加载')
  }
})

// 组件卸载
onUnmounted(() => {
  if (simulation) {
    simulation.stop()
  }

  if (app) {
    app.destroy(true, true)
  }
})
</script>

<style scoped>
.graph-canvas {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
}

/* 确保 Pixi.js 画布填满容器 */
.graph-canvas :deep(canvas) {
  display: block;
  width: 100% !important;
  height: 100% !important;
}
</style>
